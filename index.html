<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coffee Shop Map - Powered by Google Places UI Kit & Places API (New)</title>

    <!-- Google Maps JavaScript API with Places UI Kit -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC-aLrX3HmDxCjR3STYJ3Y4TUR4z12I_mI&libraries=places,geometry&callback=initMap">
    </script>


    <!-- Google Places UI Kit -->
    <script type="importmap">
    {
        "imports": {
            "@googlemaps/places": "https://unpkg.com/@googlemaps/places@1.0.0/dist/index.min.js"
        }
    }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
            min-height: 100vh;
        }

        .container {
            display: flex;
            height: 100vh;
            position: relative;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 24px;
            width: 400px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .controls.hidden {
            transform: translateX(-100%);
        }

        .controls h1 {
            color: #1a73e8;
            margin-bottom: 24px;
            font-size: 24px;
            text-align: center;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 12px;
            font-weight: 500;
        }

        .search-section {
            margin-bottom: 24px;
        }

        .search-section h3 {
            color: #202124;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 500;
        }

        /* Google Places UI Kit Autocomplete */
        .autocomplete-container {
            position: relative;
            margin-bottom: 16px;
        }

        #placeAutocomplete {
            width: 100%;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 1px 6px rgba(26, 115, 232, 0.3);
        }

        .search-btn {
            width: 100%;
            padding: 12px;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 12px;
            transition: background-color 0.2s, box-shadow 0.2s;
        }

        .search-btn:hover {
            background: #1557b0;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.4);
        }

        .search-btn:disabled {
            background: #f1f3f4;
            color: #5f6368;
            cursor: not-allowed;
            box-shadow: none;
        }

        .info-section {
            background: rgba(248, 249, 250, 0.9);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e8eaed;
        }

        .info-section h4 {
            color: #202124;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .info-section p {
            color: #5f6368;
            font-size: 12px;
            line-height: 1.4;
        }

        .api-info {
            background: linear-gradient(135deg, #e8f0fe 0%, #f8f9fa 100%);
            border-left: 4px solid #1a73e8;
        }

        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            color: #1a73e8;
        }

        .stat-label {
            font-size: 10px;
            color: #5f6368;
            text-transform: uppercase;
            font-weight: 500;
        }

        #map {
            flex: 1;
            height: 100%;
            position: relative;
            min-height: 400px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            z-index: 2000;
            display: none;
            text-align: center;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f1f3f4;
            border-top: 3px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #202124;
            font-size: 14px;
            font-weight: 500;
        }

        /* Places UI Kit Components Styling */
        gmp-place-overview {
            width: 100%;
            margin: 16px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        gmp-place-list {
            width: 100%;
            max-height: 400px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Top Rated Coffee Shops Sidebar */
        .top-rated-sidebar {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            max-height: 80vh;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            z-index: 1000;
            overflow: hidden;
            display: none;
            transition: transform 0.3s ease;
        }

        .top-rated-sidebar.show {
            display: block;
        }

        .top-rated-sidebar.hidden {
            transform: translateX(100%);
        }

        .sidebar-header {
            background: linear-gradient(45deg, #1a73e8, #4285f4);
            color: white;
            padding: 16px 20px;
            position: relative;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }

        .sidebar-header .close-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .sidebar-header .close-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar-content {
            max-height: calc(80vh - 60px);
            overflow-y: auto;
            padding: 0;
        }

        .coffee-shop-item {
            border-bottom: 1px solid #e8eaed;
            transition: background-color 0.2s;
        }

        .coffee-shop-item:last-child {
            border-bottom: none;
        }

        .coffee-shop-header {
            padding: 16px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .coffee-shop-header:hover {
            background-color: #f8f9fa;
        }

        .coffee-shop-details {
            padding: 0 20px 16px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #e8eaed;
            display: none;
            animation: slideDown 0.3s ease-out;
        }

        .coffee-shop-details.show {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
            }
        }

        .detail-photo {
            width: 100%;
            max-width: 200px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 12px;
        }

        .detail-section {
            margin-bottom: 12px;
            font-size: 12px;
        }

        .detail-section h5 {
            margin: 0 0 4px 0;
            font-size: 13px;
            font-weight: 500;
            color: #202124;
        }

        .hours-list {
            font-size: 11px;
            color: #5f6368;
            line-height: 1.3;
        }

        .review-item {
            background: white;
            padding: 8px;
            border-radius: 6px;
            margin-bottom: 8px;
            border-left: 3px solid #fbbc04;
        }

        .review-text {
            font-size: 11px;
            color: #202124;
            line-height: 1.4;
            margin-bottom: 4px;
        }

        .review-author {
            font-size: 10px;
            color: #5f6368;
        }

        .action-buttons {
            display: flex;
            gap: 6px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 6px 10px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 10px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: opacity 0.2s;
        }

        .action-btn:hover {
            opacity: 0.8;
        }

        .btn-website { background: #1a73e8; color: white; }
        .btn-google { background: #ea4335; color: white; }
        .btn-streetview { background: #34a853; color: white; }

        .shop-name {
            font-weight: 500;
            color: #202124;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .shop-rating {
            color: #fbbc04;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .shop-address {
            color: #5f6368;
            font-size: 11px;
            line-height: 1.3;
        }

        .shop-details {
            margin-top: 8px;
            font-size: 11px;
        }

        .shop-price {
            display: inline-block;
            background: #e8f5e8;
            color: #137333;
            padding: 2px 6px;
            border-radius: 4px;
            margin-right: 8px;
        }

        .shop-hours {
            color: #5f6368;
        }

        /* Toggle buttons for hidden panels */
        .toggle-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 18px;
            display: none;
        }

        .toggle-controls.show {
            display: block;
        }

        .toggle-sidebar {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: rgba(26, 115, 232, 0.9);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 18px;
            display: none;
        }

        .toggle-sidebar.show {
            display: block;
        }

        .controls .hide-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #5f6368;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .controls .hide-btn:hover {
            background-color: #f1f3f4;
            color: #202124;
        }

        @media (max-width: 768px) {
            .controls {
                width: 100%;
                height: 100vh;
                position: absolute;
                top: 0;
                left: 0;
            }

            .top-rated-sidebar {
                width: calc(100% - 40px);
                right: 20px;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Toggle buttons for hidden panels -->
    <button class="toggle-controls" id="toggleControls">☰</button>
    <button class="toggle-sidebar" id="toggleSidebar">⭐</button>

    <div class="container">
        <!-- Controls Panel -->
        <div class="controls" id="controls">
            <button class="hide-btn" id="hideControls">&times;</button>

            <h1>☕ Coffee Shop Discovery</h1>

            <div class="search-section">
                <h3>🔍 Find Coffee Shops</h3>

                <!-- Google Places Autocomplete -->
                <div class="autocomplete-container">
                    <input type="text" id="placeAutocomplete" class="search-input" placeholder="Enter city or address (e.g., Seattle, WA)">
                </div>

                <!-- Alternative text input for manual search -->
                <div class="autocomplete-container" style="margin-top: 12px;">
                    <input type="text" id="manualLocationInput" class="search-input" placeholder="Or type location manually and click search">
                </div>

                <button class="search-btn" id="searchLocation">Search Coffee Shops</button>
                <button class="search-btn" id="loadNearby" style="background: #137333; margin-top: 8px;">Load Nearby Coffee Shops</button>
            </div>

            <div class="info-section api-info">
                <h4>🚀 Powered by Google Places</h4>
                <p>This app uses <strong>Google Places UI Kit</strong> and <strong>Places API (New)</strong> for the most accurate and comprehensive coffee shop data.</p>
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number" id="coffeeCount">0</div>
                        <div class="stat-label">Coffee Shops</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="avgRating">0.0</div>
                        <div class="stat-label">Avg Rating</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="topRated">0</div>
                        <div class="stat-label">Top Rated</div>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h4>💡 How to Use</h4>
                <p>• Use the <strong>autocomplete field</strong> - start typing and select from suggestions<br>
                • Or type in the <strong>manual input field</strong> and click "Search Coffee Shops"<br>
                • Click "Load Nearby" to find coffee shops around your current map view<br>
                • Click on <strong>coffee markers</strong> on the map for detailed information<br>
                • Use the top-rated sidebar to discover the best coffee spots</p>
            </div>
        </div>

        <!-- Map Container -->
        <div id="map"></div>
    </div>

    <!-- Top Rated Coffee Shops Sidebar -->
    <div class="top-rated-sidebar" id="topRatedSidebar">
        <div class="sidebar-header">
            <h3>⭐ Top Rated Coffee Shops</h3>
            <button class="close-btn" id="closeSidebar">&times;</button>
        </div>
        <div class="sidebar-content" id="sidebarContent">
            <p style="text-align: center; color: #5f6368; padding: 20px;">Search for coffee shops to see top rated places in the area</p>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div class="loading-text">Finding amazing coffee shops...</div>
    </div>

    <!-- Street View Modal -->
    <div id="streetViewModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 2000; backdrop-filter: blur(5px);">
        <div style="position: relative; width: 90%; height: 90%; margin: 2.5% auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">
            <div style="background: linear-gradient(45deg, #34a853, #4285f4); color: white; padding: 16px 20px; display: flex; justify-content: space-between; align-items: center;">
                <h3 id="streetViewTitle" style="margin: 0; font-size: 18px; font-weight: 500;">🏪 Street View</h3>
                <button onclick="closeStreetView()" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 50%; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'" onmouseout="this.style.backgroundColor='transparent'">&times;</button>
            </div>
            <div id="streetViewContainer" style="width: 100%; height: calc(100% - 68px);"></div>
        </div>
    </div>

    <!-- Initialize Google Maps when API is loaded -->
    <script>
        // Global variables
        let map;
        let service;
        let coffeeMarkers = [];
        let coffeeShopsData = [];
        let autocomplete;

        // Initialize the application when Google Maps API is loaded
        function initMap() {
            console.log('🚀 Initializing Google Maps with Places UI Kit...');

            try {
                // Check if Google Maps API is available
                if (typeof google === 'undefined' || !google.maps) {
                    throw new Error('Google Maps API not loaded');
                }

                // Initialize Google Map
                map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 39.8283, lng: -98.5795 }, // Center of US
                    zoom: 4,
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    styles: [
                        {
                            featureType: 'poi.business',
                            elementType: 'labels',
                            stylers: [{ visibility: 'off' }]
                        }
                    ]
                });

                // Initialize Places Service
                service = new google.maps.places.PlacesService(map);
                console.log('✅ Places service initialized:', !!service);

                // Wait a bit for the map to fully load before initializing UI Kit
                setTimeout(() => {
                    initializePlacesUIKit();
                }, 1000);

                // Set up event listeners
                setupEventListeners();

                console.log('✅ Map and Places services initialized successfully');

            } catch (error) {
                console.error('❌ Error initializing map:', error);
                alert('Error loading Google Maps. Please refresh the page and try again.');
            }
        }

        // Make initMap available globally for Google Maps API callback
        window.initMap = initMap;
    </script>

    <script>
        // Configuration - Using new API key for both Places UI Kit and Places API (New)
        const GOOGLE_API_KEY = 'AIzaSyC-aLrX3HmDxCjR3STYJ3Y4TUR4z12I_mI';

        // UI Elements
        const loading = document.getElementById('loading');
        const searchButton = document.getElementById('searchLocation');
        const loadNearbyButton = document.getElementById('loadNearby');
        const sidebar = document.getElementById('topRatedSidebar');
        const sidebarContent = document.getElementById('sidebarContent');
        const toggleControls = document.getElementById('toggleControls');
        const toggleSidebar = document.getElementById('toggleSidebar');
        const hideControls = document.getElementById('hideControls');
        const closeSidebar = document.getElementById('closeSidebar');
        const controls = document.getElementById('controls');

        // Stats elements
        const coffeeCountEl = document.getElementById('coffeeCount');
        const avgRatingEl = document.getElementById('avgRating');
        const topRatedEl = document.getElementById('topRated');

        // Initialize Google Places Autocomplete (standard implementation)
        function initializePlacesUIKit() {
            const autocompleteElement = document.getElementById('placeAutocomplete');

            if (autocompleteElement) {
                console.log('🔧 Setting up Google Places Autocomplete...');

                // Create standard Google Places Autocomplete
                const autocomplete = new google.maps.places.Autocomplete(autocompleteElement, {
                    types: ['(cities)'], // Restrict to cities and addresses
                    fields: ['place_id', 'geometry', 'name', 'formatted_address']
                });

                // Listen for place selection
                autocomplete.addListener('place_changed', () => {
                    const place = autocomplete.getPlace();
                    console.log('📍 Place selected:', place);

                    if (place.geometry && place.geometry.location) {
                        console.log('📍 Place location:', place.geometry.location.toString());

                        // Center map on selected location
                        map.setCenter(place.geometry.location);
                        map.setZoom(13);

                        // Search for coffee shops in this area
                        searchCoffeeShopsNearby(place.geometry.location);
                    } else {
                        console.warn('⚠️ No geometry found for selected place');
                        alert('Please select a place from the dropdown suggestions.');
                    }
                });

                console.log('✅ Google Places Autocomplete initialized');
            } else {
                console.warn('⚠️ Autocomplete element not found');
            }
        }

        // Set up event listeners
        function setupEventListeners() {
            // Search button - Now uses manual input field
            searchButton.addEventListener('click', () => {
                const manualInput = document.getElementById('manualLocationInput');
                const inputValue = manualInput.value?.trim();

                console.log('🔍 Search button clicked, manual input value:', inputValue);

                if (inputValue) {
                    showLoading(true);
                    // Use Google Geocoding to get coordinates from text input
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({ address: inputValue }, (results, status) => {
                        console.log('🌍 Geocoding result:', status, results);

                        if (status === 'OK' && results[0]) {
                            const location = results[0].geometry.location;
                            console.log('📍 Found location:', location.toString());
                            map.setCenter(location);
                            map.setZoom(13);
                            searchCoffeeShopsNearby(location);
                        } else {
                            showLoading(false);
                            console.error('❌ Geocoding failed:', status);
                            alert(`Location not found (${status}). Please try a different search term.`);
                        }
                    });
                } else {
                    alert('Please enter a location in the text field, or use the autocomplete dropdown above.');
                }
            });

            // Load nearby button
            loadNearbyButton.addEventListener('click', () => {
                const center = map.getCenter();
                console.log('🎯 Loading nearby coffee shops at:', center.toString());
                searchCoffeeShopsNearby(center);
            });



            // UI toggle buttons
            hideControls.addEventListener('click', () => {
                controls.classList.add('hidden');
                toggleControls.classList.add('show');
            });

            toggleControls.addEventListener('click', () => {
                controls.classList.remove('hidden');
                toggleControls.classList.remove('show');
            });

            closeSidebar.addEventListener('click', () => {
                sidebar.classList.remove('show');
                toggleSidebar.classList.add('show');
            });

            toggleSidebar.addEventListener('click', () => {
                sidebar.classList.add('show');
                toggleSidebar.classList.remove('show');
            });

            // Add Enter key support for manual search input
            const manualInput = document.getElementById('manualLocationInput');
            if (manualInput) {
                manualInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchButton.click();
                    }
                });
            }
        }

        // Function to show/hide loading
        function showLoading(show) {
            loading.classList.toggle('show', show);
        }



        // Search for coffee shops near a location using Google Places API
        function searchCoffeeShopsNearby(location) {
            showLoading(true);
            clearMarkers();

            console.log('🔍 Searching for coffee shops near:', location);
            console.log('🔧 Places service available:', !!service);

            if (!service) {
                console.error('❌ Places service not initialized');
                alert('Places service not ready. Please wait a moment and try again.');
                showLoading(false);
                return;
            }

            // Create request for nearby search
            const request = {
                location: location,
                radius: 5000, // 5km radius
                type: 'cafe', // Fixed: should be string, not array
                keyword: 'coffee',
                openNow: false // Don't restrict to only open places
            };

            console.log('📋 Search request:', request);

            // Perform the nearby search
            service.nearbySearch(request, (results, status) => {
                console.log('📊 Places API response:', status, results ? results.length : 0, 'results');

                if (status === google.maps.places.PlacesServiceStatus.OK && results) {
                    console.log(`✅ Found ${results.length} potential coffee shops`);
                    console.log('🏪 Sample results:', results.slice(0, 3).map(r => ({ name: r.name, types: r.types })));

                    // Filter for coffee-related places
                    const coffeeShops = results.filter(place => isCoffeeRelated(place.name, place.types));

                    console.log(`☕ Filtered to ${coffeeShops.length} coffee shops`);

                    if (coffeeShops.length === 0) {
                        console.warn('⚠️ No coffee shops found after filtering');
                        alert('No coffee shops found in this area. Try searching in a different location or expanding the search radius.');
                        showLoading(false);
                        return;
                    }

                    // Sort by rating (highest first)
                    coffeeShops.sort((a, b) => (b.rating || 0) - (a.rating || 0));

                    // Store the data
                    coffeeShopsData = coffeeShops;

                    // Add markers to map
                    console.log(`🎯 Adding ${coffeeShops.length} markers to map...`);
                    coffeeShops.forEach((shop, index) => {
                        console.log(`📍 Processing shop ${index + 1}:`, shop.name);
                        console.log(`📍 Shop geometry:`, shop.geometry);
                        console.log(`📍 Shop location:`, shop.geometry?.location);
                        console.log(`📍 Full shop data:`, shop);
                        addCoffeeShopMarker(shop);
                    });
                    console.log(`✅ Finished adding markers. Total markers: ${coffeeMarkers.length}`);

                    // Update sidebar and stats
                    updateSidebar(coffeeShops);
                    updateStats(coffeeShops);

                    // Show sidebar
                    sidebar.classList.add('show');
                    toggleSidebar.classList.remove('show');

                } else {
                    console.error('❌ Places search failed:', status);

                    // Try a fallback search with different parameters
                    if (status === google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
                        console.log('🔄 Trying fallback search with broader parameters...');
                        const fallbackRequest = {
                            location: location,
                            radius: 10000, // Increase radius to 10km
                            keyword: 'coffee shop starbucks cafe'
                        };

                        service.nearbySearch(fallbackRequest, (fallbackResults, fallbackStatus) => {
                            if (fallbackStatus === google.maps.places.PlacesServiceStatus.OK && fallbackResults && fallbackResults.length > 0) {
                                console.log(`✅ Fallback search found ${fallbackResults.length} results`);

                                const coffeeShops = fallbackResults.filter(place => isCoffeeRelated(place.name, place.types));

                                if (coffeeShops.length > 0) {
                                    coffeeShops.sort((a, b) => (b.rating || 0) - (a.rating || 0));
                                    coffeeShopsData = coffeeShops;

                                    coffeeShops.forEach(shop => {
                                        addCoffeeShopMarker(shop);
                                    });

                                    updateSidebar(coffeeShops);
                                    updateStats(coffeeShops);
                                    sidebar.classList.add('show');
                                    toggleSidebar.classList.remove('show');
                                    showLoading(false);
                                    return;
                                }
                            }

                            // If fallback also fails, show error
                            showFinalError(status);
                        });
                        return;
                    }

                    showFinalError(status);
                }

                showLoading(false);
            });
        }

        // Show final error message
        function showFinalError(status) {
            let errorMessage = 'Error searching for coffee shops. ';

            switch(status) {
                case google.maps.places.PlacesServiceStatus.ZERO_RESULTS:
                    errorMessage += 'No coffee shops found in this area. Try a different location.';
                    break;
                case google.maps.places.PlacesServiceStatus.OVER_QUERY_LIMIT:
                    errorMessage += 'Query limit exceeded. Please try again later.';
                    break;
                case google.maps.places.PlacesServiceStatus.REQUEST_DENIED:
                    errorMessage += 'Request denied. Please check API key permissions.';
                    break;
                case google.maps.places.PlacesServiceStatus.INVALID_REQUEST:
                    errorMessage += 'Invalid request parameters.';
                    break;
                default:
                    errorMessage += 'Please try again.';
            }

            alert(errorMessage);
            showLoading(false);
        }

        // Enhanced coffee shop filtering
        function isCoffeeRelated(name, types) {
            if (!name) return false;

            const coffeeKeywords = [
                'coffee', 'cafe', 'espresso', 'latte', 'cappuccino', 'roaster', 'roastery',
                'brew', 'bean', 'grind', 'barista', 'americano', 'macchiato', 'mocha',
                'starbucks', 'dunkin', 'peet', 'blue bottle', 'intelligentsia',
                'counter culture', 'ritual', 'philz', 'caribou', 'tim hortons',
                'costa', 'nero', 'lavazza', 'illy', 'folgers', 'maxwell house'
            ];

            const coffeeTypes = ['cafe', 'coffee_shop', 'bakery'];
            const strongExclusions = [
                'gas_station', 'convenience_store', 'supermarket', 'grocery_or_supermarket',
                'night_club', 'bar', 'liquor_store', 'pharmacy', 'hospital', 'bank',
                'atm', 'car_dealer', 'car_repair', 'clothing_store', 'electronics_store'
            ];

            const nameLower = name.toLowerCase();

            // Check for strong exclusions first
            if (types && types.some(type => strongExclusions.includes(type))) {
                return false;
            }

            // Check for coffee-related keywords in name
            const hasKeyword = coffeeKeywords.some(keyword => nameLower.includes(keyword));

            // Check for coffee-related types
            const hasType = types && types.some(type => coffeeTypes.includes(type));

            return hasKeyword || hasType;
        }

        // Clear all markers from the map
        function clearMarkers() {
            coffeeMarkers.forEach(marker => {
                marker.setMap(null);
            });
            coffeeMarkers = [];
        }

        // Add a coffee shop marker to the map - Simplified and reliable implementation
        function addCoffeeShopMarker(place) {
            console.log('📍 Adding marker for:', place.name);

            if (!place.geometry || !place.geometry.location) {
                console.error('❌ No geometry/location for place:', place.name);
                return;
            }

            // Extract position from location object
            let position;
            const location = place.geometry.location;

            // Handle both LatLng objects and plain objects
            if (typeof location.lat === 'function') {
                position = { lat: location.lat(), lng: location.lng() };
            } else {
                position = { lat: location.lat, lng: location.lng };
            }

            console.log('📍 Creating marker at position:', position);

            // Create marker with simple star icon
            const marker = new google.maps.Marker({
                position: position,
                map: map,
                title: place.name,
                icon: {
                    path: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
                    fillColor: '#FFD700',
                    fillOpacity: 1,
                    strokeColor: '#FF6B35',
                    strokeWeight: 2,
                    scale: 1.2,
                    anchor: new google.maps.Point(12, 12)
                },
                animation: google.maps.Animation.DROP,
                zIndex: 100
            });

            console.log('✅ Marker created successfully');

            // Create info window content
            const rating = place.rating ? place.rating.toFixed(1) : 'No rating';
            const priceLevel = place.price_level ? '$'.repeat(place.price_level) : 'Price not available';
            const reviewCount = place.user_ratings_total || 0;

            let status = 'Hours unknown';
            if (place.opening_hours) {
                if (place.opening_hours.open_now === true) {
                    status = '<span style="color: #137333; font-weight: 500;">Open now</span>';
                } else if (place.opening_hours.open_now === false) {
                    status = '<span style="color: #d93025; font-weight: 500;">Closed</span>';
                }
            }

            const infoContent = `
                <div style="max-width: 280px; font-family: 'Google Sans', Arial, sans-serif; line-height: 1.4;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 18px; margin-right: 6px;">☕</span>
                        <h3 style="color: #1a73e8; margin: 0; font-size: 16px; font-weight: 500;">${place.name}</h3>
                    </div>
                    <div style="margin-bottom: 6px;">
                        <span style="color: #fbbc04; font-size: 14px;">★ ${rating}</span>
                        <span style="color: #5f6368; font-size: 12px; margin-left: 4px;">(${reviewCount} reviews)</span>
                    </div>
                    <div style="margin-bottom: 6px; font-size: 13px;">
                        <strong>Price:</strong> <span style="color: #137333;">${priceLevel}</span>
                    </div>
                    <div style="margin-bottom: 8px; font-size: 13px;">
                        <strong>Status:</strong> ${status}
                    </div>
                    <div style="color: #5f6368; font-size: 12px; margin-bottom: 12px;">
                        📍 ${place.vicinity || place.formatted_address || 'Address not available'}
                    </div>
                    <div style="margin-bottom: 8px;">
                        <button onclick="openStreetView(${position.lat}, ${position.lng}, '${place.name.replace(/'/g, "\\'")}')" style="background: #34a853; color: white; padding: 6px 12px; border-radius: 4px; border: none; font-size: 11px; font-weight: 500; cursor: pointer;">🏪 Street View</button>
                    </div>
                    <div style="font-size: 10px; color: #9aa0a6; border-top: 1px solid #e8eaed; padding-top: 6px;">
                        Powered by Google Places API
                    </div>
                </div>
            `;

            const infoWindow = new google.maps.InfoWindow({
                content: infoContent,
                maxWidth: 300
            });

            // Add click listener to marker
            marker.addListener('click', () => {
                console.log('🖱️ Marker clicked:', place.name);

                // Close all other info windows
                coffeeMarkers.forEach(m => {
                    if (m.infoWindow && m.infoWindow !== infoWindow) {
                        m.infoWindow.close();
                    }
                });

                // Get detailed place information if place_id is available
                if (place.place_id && service) {
                    console.log('🔍 Fetching detailed place info for:', place.name);

                    const detailsRequest = {
                        placeId: place.place_id,
                        fields: [
                            'name', 'rating', 'user_ratings_total', 'price_level',
                            'opening_hours', 'formatted_address', 'formatted_phone_number',
                            'website', 'url', 'photos', 'reviews', 'types'
                        ]
                    };

                    service.getDetails(detailsRequest, (detailedPlace, status) => {
                        if (status === google.maps.places.PlacesServiceStatus.OK && detailedPlace) {
                            console.log('✅ Got detailed place info:', detailedPlace);
                            updateInfoWindowWithDetails(infoWindow, detailedPlace, marker);
                        } else {
                            console.warn('⚠️ Could not get detailed place info:', status);
                            // Open basic info window as fallback
                            infoWindow.open(map, marker);
                        }
                    });
                } else {
                    // Open basic info window if no place_id
                    infoWindow.open(map, marker);
                }
            });

            // Store reference to info window for later access
            marker.infoWindow = infoWindow;
            marker.placeData = place;

            // Add to markers array
            coffeeMarkers.push(marker);

            console.log(`✅ Coffee shop marker added. Total markers: ${coffeeMarkers.length}`);
        }

        // Update info window with detailed place information
        function updateInfoWindowWithDetails(infoWindow, detailedPlace, marker) {
            console.log('📝 Updating info window with detailed place info');

            const rating = detailedPlace.rating ? detailedPlace.rating.toFixed(1) : 'No rating';
            const priceLevel = detailedPlace.price_level ? '$'.repeat(detailedPlace.price_level) : 'Price not available';
            const reviewCount = detailedPlace.user_ratings_total || 0;
            const phone = detailedPlace.formatted_phone_number || '';
            const website = detailedPlace.website || '';
            const googleUrl = detailedPlace.url || '';

            let status = 'Hours unknown';
            let hoursInfo = '';
            if (detailedPlace.opening_hours) {
                if (detailedPlace.opening_hours.open_now === true) {
                    status = '<span style="color: #137333; font-weight: 500;">Open now</span>';
                } else if (detailedPlace.opening_hours.open_now === false) {
                    status = '<span style="color: #d93025; font-weight: 500;">Closed</span>';
                }

                // Add today's hours if available
                if (detailedPlace.opening_hours.weekday_text && detailedPlace.opening_hours.weekday_text.length > 0) {
                    const today = new Date().getDay();
                    const todayIndex = today === 0 ? 6 : today - 1; // Convert Sunday=0 to Monday=0 format
                    if (detailedPlace.opening_hours.weekday_text[todayIndex]) {
                        hoursInfo = `<div style="font-size: 11px; color: #5f6368; margin-top: 4px;">${detailedPlace.opening_hours.weekday_text[todayIndex]}</div>`;
                    }
                }
            }

            // Get photo if available
            let photoHtml = '';
            if (detailedPlace.photos && detailedPlace.photos.length > 0) {
                const photoUrl = detailedPlace.photos[0].getUrl({ maxWidth: 200, maxHeight: 150 });
                photoHtml = `<img src="${photoUrl}" style="width: 100%; max-width: 200px; height: auto; border-radius: 6px; margin-bottom: 8px;" alt="${detailedPlace.name}">`;
            }

            // Get recent review if available
            let reviewHtml = '';
            if (detailedPlace.reviews && detailedPlace.reviews.length > 0) {
                const review = detailedPlace.reviews[0];
                const reviewText = review.text.length > 100 ? review.text.substring(0, 100) + '...' : review.text;
                reviewHtml = `
                    <div style="margin-top: 8px; padding: 8px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #fbbc04;">
                        <div style="font-size: 11px; color: #5f6368; margin-bottom: 2px;">Recent Review:</div>
                        <div style="font-size: 12px; color: #202124;">"${reviewText}"</div>
                        <div style="font-size: 10px; color: #5f6368; margin-top: 4px;">- ${review.author_name}</div>
                    </div>
                `;
            }

            const detailedContent = `
                <div style="max-width: 300px; font-family: 'Google Sans', Arial, sans-serif; line-height: 1.4;">
                    ${photoHtml}
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 18px; margin-right: 6px;">⭐</span>
                        <h3 style="color: #1a73e8; margin: 0; font-size: 16px; font-weight: 500;">${detailedPlace.name}</h3>
                    </div>
                    <div style="margin-bottom: 6px;">
                        <span style="color: #fbbc04; font-size: 14px;">★ ${rating}</span>
                        <span style="color: #5f6368; font-size: 12px; margin-left: 4px;">(${reviewCount} reviews)</span>
                    </div>
                    <div style="margin-bottom: 6px; font-size: 13px;">
                        <strong>Price:</strong> <span style="color: #137333;">${priceLevel}</span>
                    </div>
                    <div style="margin-bottom: 4px; font-size: 13px;">
                        <strong>Status:</strong> ${status}
                    </div>
                    ${hoursInfo}
                    ${phone ? `<div style="margin-bottom: 6px; font-size: 12px;"><strong>📞</strong> ${phone}</div>` : ''}
                    <div style="color: #5f6368; font-size: 12px; margin-bottom: 12px;">
                        📍 ${detailedPlace.formatted_address || 'Address not available'}
                    </div>
                    ${reviewHtml}
                    <div style="margin-top: 12px; display: flex; gap: 6px; flex-wrap: wrap;">
                        ${website ? `<a href="${website}" target="_blank" style="background: #1a73e8; color: white; padding: 6px 10px; border-radius: 4px; text-decoration: none; font-size: 11px; font-weight: 500;">🌐 Website</a>` : ''}
                        ${googleUrl ? `<a href="${googleUrl}" target="_blank" style="background: #ea4335; color: white; padding: 6px 10px; border-radius: 4px; text-decoration: none; font-size: 11px; font-weight: 500;">📍 View on Google</a>` : ''}
                        <button onclick="openStreetView(${detailedPlace.geometry.location.lat()}, ${detailedPlace.geometry.location.lng()}, '${detailedPlace.name.replace(/'/g, "\\'")}')" style="background: #34a853; color: white; padding: 6px 10px; border-radius: 4px; border: none; font-size: 11px; font-weight: 500; cursor: pointer;">🏪 Street View</button>
                    </div>
                    <div style="font-size: 10px; color: #9aa0a6; border-top: 1px solid #e8eaed; padding-top: 6px; margin-top: 8px;">
                        Powered by Google Places API
                    </div>
                </div>
            `;

            infoWindow.setContent(detailedContent);
            infoWindow.open(map, marker);
        }

        // Street View functionality
        let streetViewPanorama = null;

        function openStreetView(lat, lng, shopName) {
            console.log('🏪 Opening Street View for:', shopName, 'at', lat, lng);

            const modal = document.getElementById('streetViewModal');
            const title = document.getElementById('streetViewTitle');
            const container = document.getElementById('streetViewContainer');

            // Update title
            title.textContent = `🏪 Street View - ${shopName}`;

            // Show modal
            modal.style.display = 'block';

            // Initialize Street View
            const position = { lat: lat, lng: lng };

            // Street View options
            const streetViewOptions = {
                position: position,
                pov: {
                    heading: 0, // Will be adjusted to face the building
                    pitch: 0
                },
                zoom: 1,
                addressControl: true,
                linksControl: true,
                panControl: true,
                enableCloseButton: false,
                fullscreenControl: true,
                motionTracking: false,
                motionTrackingControl: false
            };

            // Create Street View panorama
            streetViewPanorama = new google.maps.StreetViewPanorama(container, streetViewOptions);

            // Try to find the best heading to face the building
            // We'll use the Street View service to get nearby panorama data
            const streetViewService = new google.maps.StreetViewService();

            streetViewService.getPanorama({
                location: position,
                radius: 50,
                source: google.maps.StreetViewSource.OUTDOOR
            }, (data, status) => {
                if (status === 'OK' && data && data.location) {
                    console.log('✅ Found Street View panorama');

                    // Calculate heading from panorama position to coffee shop position
                    const panoPosition = data.location.latLng;
                    const heading = google.maps.geometry.spherical.computeHeading(panoPosition, position);

                    // Update the panorama with the calculated heading
                    streetViewPanorama.setPov({
                        heading: heading,
                        pitch: 0
                    });

                    streetViewPanorama.setPosition(data.location.latLng);
                } else {
                    console.warn('⚠️ Street View not available at this location:', status);
                    // Keep the original position and heading
                }
            });

            // Add escape key listener
            document.addEventListener('keydown', handleStreetViewEscape);
        }

        function closeStreetView() {
            console.log('🚪 Closing Street View');

            const modal = document.getElementById('streetViewModal');
            modal.style.display = 'none';

            // Clean up Street View panorama
            if (streetViewPanorama) {
                streetViewPanorama = null;
            }

            // Remove escape key listener
            document.removeEventListener('keydown', handleStreetViewEscape);
        }

        function handleStreetViewEscape(event) {
            if (event.key === 'Escape') {
                closeStreetView();
            }
        }

        // Make functions globally available
        window.openStreetView = openStreetView;
        window.closeStreetView = closeStreetView;

        // Update the sidebar with top coffee shops
        function updateSidebar(coffeeShops) {
            sidebarContent.innerHTML = '';

            const topShops = coffeeShops.slice(0, 10); // Show top 10

            topShops.forEach((shop, index) => {
                const shopElement = document.createElement('div');
                shopElement.className = 'coffee-shop-item';

                const rating = shop.rating || 'No rating';
                const priceLevel = shop.price_level ? '$'.repeat(shop.price_level) : '';
                const status = shop.opening_hours?.open_now ? 'Open now' :
                              shop.opening_hours?.open_now === false ? 'Closed' : '';

                // Create the header (always visible)
                const headerElement = document.createElement('div');
                headerElement.className = 'coffee-shop-header';
                headerElement.innerHTML = `
                    <div class="shop-name">${index + 1}. ${shop.name}</div>
                    <div class="shop-rating">⭐ ${rating} (${shop.user_ratings_total || 0} reviews)</div>
                    <div class="shop-address">${shop.vicinity || shop.formatted_address || 'Address not available'}</div>
                    <div class="shop-details">
                        ${priceLevel ? `<span class="shop-price">${priceLevel}</span>` : ''}
                        ${status ? `<span class="shop-hours">${status}</span>` : ''}
                    </div>
                `;

                // Create the details section (initially hidden)
                const detailsElement = document.createElement('div');
                detailsElement.className = 'coffee-shop-details';
                detailsElement.innerHTML = `
                    <div style="text-align: center; color: #5f6368; font-size: 11px; padding: 12px;">
                        Click to load detailed information...
                    </div>
                `;

                // Add click handler to header for dropdown toggle
                headerElement.addEventListener('click', (e) => {
                    e.stopPropagation();

                    // Toggle this dropdown
                    const isCurrentlyOpen = detailsElement.classList.contains('show');

                    // Close all other dropdowns
                    document.querySelectorAll('.coffee-shop-details.show').forEach(detail => {
                        detail.classList.remove('show');
                    });

                    if (!isCurrentlyOpen) {
                        // Open this dropdown and load detailed info
                        detailsElement.classList.add('show');
                        loadDetailedShopInfo(shop, detailsElement);

                        // Also center map on this coffee shop
                        centerMapOnShop(shop);
                    }
                });

                shopElement.appendChild(headerElement);
                shopElement.appendChild(detailsElement);
                sidebarContent.appendChild(shopElement);
            });
        }

        // Load detailed information for a coffee shop
        function loadDetailedShopInfo(shop, detailsElement) {
            console.log('🔍 Loading detailed info for:', shop.name);

            if (!shop.place_id || !service) {
                detailsElement.innerHTML = `
                    <div style="text-align: center; color: #ea4335; font-size: 11px; padding: 12px;">
                        Detailed information not available
                    </div>
                `;
                return;
            }

            // Show loading state
            detailsElement.innerHTML = `
                <div style="text-align: center; color: #5f6368; font-size: 11px; padding: 12px;">
                    <div style="display: inline-block; width: 16px; height: 16px; border: 2px solid #f3f3f3; border-top: 2px solid #1a73e8; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 8px;"></div>
                    Loading details...
                </div>
            `;

            const detailsRequest = {
                placeId: shop.place_id,
                fields: [
                    'name', 'rating', 'user_ratings_total', 'price_level',
                    'opening_hours', 'formatted_address', 'formatted_phone_number',
                    'website', 'url', 'photos', 'reviews', 'types'
                ]
            };

            service.getDetails(detailsRequest, (detailedPlace, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK && detailedPlace) {
                    console.log('✅ Got detailed place info for sidebar:', detailedPlace);
                    renderDetailedShopInfo(detailedPlace, detailsElement);
                } else {
                    console.warn('⚠️ Could not get detailed place info:', status);
                    detailsElement.innerHTML = `
                        <div style="text-align: center; color: #ea4335; font-size: 11px; padding: 12px;">
                            Could not load detailed information
                        </div>
                    `;
                }
            });
        }

        // Render detailed shop information in the sidebar
        function renderDetailedShopInfo(detailedPlace, detailsElement) {
            const phone = detailedPlace.formatted_phone_number || '';
            const website = detailedPlace.website || '';
            const googleUrl = detailedPlace.url || '';

            // Get photo if available
            let photoHtml = '';
            if (detailedPlace.photos && detailedPlace.photos.length > 0) {
                const photoUrl = detailedPlace.photos[0].getUrl({ maxWidth: 200, maxHeight: 120 });
                photoHtml = `<img src="${photoUrl}" class="detail-photo" alt="${detailedPlace.name}">`;
            }

            // Get hours information
            let hoursHtml = '';
            if (detailedPlace.opening_hours && detailedPlace.opening_hours.weekday_text) {
                const hours = detailedPlace.opening_hours.weekday_text.map(day => `<div>${day}</div>`).join('');
                hoursHtml = `
                    <div class="detail-section">
                        <h5>📅 Hours</h5>
                        <div class="hours-list">${hours}</div>
                    </div>
                `;
            }

            // Get reviews
            let reviewsHtml = '';
            if (detailedPlace.reviews && detailedPlace.reviews.length > 0) {
                const topReviews = detailedPlace.reviews.slice(0, 3).map(review => {
                    const reviewText = review.text.length > 120 ? review.text.substring(0, 120) + '...' : review.text;
                    return `
                        <div class="review-item">
                            <div class="review-text">"${reviewText}"</div>
                            <div class="review-author">⭐ ${review.rating}/5 - ${review.author_name}</div>
                        </div>
                    `;
                }).join('');

                reviewsHtml = `
                    <div class="detail-section">
                        <h5>💬 Recent Reviews</h5>
                        ${topReviews}
                    </div>
                `;
            }

            // Contact information
            let contactHtml = '';
            if (phone) {
                contactHtml = `
                    <div class="detail-section">
                        <h5>📞 Contact</h5>
                        <div style="font-size: 11px; color: #5f6368;">${phone}</div>
                    </div>
                `;
            }

            // Get position for Street View
            let position = { lat: 0, lng: 0 };
            if (detailedPlace.geometry && detailedPlace.geometry.location) {
                const location = detailedPlace.geometry.location;
                if (typeof location.lat === 'function') {
                    position = { lat: location.lat(), lng: location.lng() };
                } else {
                    position = { lat: location.lat, lng: location.lng };
                }
            }

            detailsElement.innerHTML = `
                ${photoHtml}
                ${contactHtml}
                ${hoursHtml}
                ${reviewsHtml}
                <div class="action-buttons">
                    ${website ? `<a href="${website}" target="_blank" class="action-btn btn-website">🌐 Website</a>` : ''}
                    ${googleUrl ? `<a href="${googleUrl}" target="_blank" class="action-btn btn-google">📍 Google Maps</a>` : ''}
                    <button onclick="openStreetView(${position.lat}, ${position.lng}, '${detailedPlace.name.replace(/'/g, "\\'")}')" class="action-btn btn-streetview">🏪 Street View</button>
                </div>
            `;
        }

        // Center map on a coffee shop
        function centerMapOnShop(shop) {
            let position;
            const location = shop.geometry.location;

            if (typeof location.lat === 'function') {
                position = { lat: location.lat(), lng: location.lng() };
            } else {
                position = { lat: location.lat, lng: location.lng };
            }

            console.log('🎯 Centering map on:', shop.name, 'at position:', position);

            // Center map on this coffee shop
            map.setCenter(position);
            map.setZoom(16);

            // Find and open the corresponding marker info window
            const marker = coffeeMarkers.find(m => {
                const markerPos = m.getPosition();
                const latDiff = Math.abs(markerPos.lat() - position.lat);
                const lngDiff = Math.abs(markerPos.lng() - position.lng);
                return latDiff < 0.0001 && lngDiff < 0.0001;
            });

            if (marker && marker.infoWindow) {
                console.log('📍 Found matching marker, opening info window');

                // Close all other info windows
                coffeeMarkers.forEach(m => {
                    if (m.infoWindow && m.infoWindow !== marker.infoWindow) {
                        m.infoWindow.close();
                    }
                });

                // Open the info window for this marker
                marker.infoWindow.open(map, marker);
            }
        }

        // Update statistics
        function updateStats(coffeeShops) {
            const count = coffeeShops.length;
            const avgRating = count > 0 ?
                (coffeeShops.reduce((sum, shop) => sum + (shop.rating || 0), 0) / count).toFixed(1) :
                '0.0';
            const topRated = coffeeShops.filter(shop => shop.rating >= 4.5).length;

            coffeeCountEl.textContent = count;
            avgRatingEl.textContent = avgRating;
            topRatedEl.textContent = topRated;
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Coffee Shop Map with Google Places UI Kit & Places API (New) ready!');
            console.log('💡 Try searching for coffee-famous cities like Seattle, Portland, or San Francisco');
            console.log('🔧 Debug: DOM loaded, Google available:', typeof google !== 'undefined');

            // Add a small delay to check if Google Maps loads
            setTimeout(() => {
                console.log('🔧 Debug: Google Maps available after delay:', typeof google !== 'undefined' && !!google.maps);
                console.log('🔧 Debug: Map initialized:', !!map);
                console.log('🔧 Debug: Service initialized:', !!service);
            }, 2000);
        });
    </script>
</body>
</html>
